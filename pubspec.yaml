name: gt_plus
description: "A new Flutter project."
publish_to: 'none'
version: 2.3.1+17

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter
  change_app_package_name: ^1.4.0
  rename_app: ^1.6.2
  get: ^4.7.2
  flutter_svg: ^2.0.17
  arkit_plugin: ^1.1.2
  device_info_plus: ^11.3.0
  image: ^4.5.3
  camera: ^0.11.0+2
  dropdown_button2: ^2.3.9
  shared_preferences: ^2.5.2
  mobile_scanner: ^6.0.6
  country_picker: ^2.0.27
  intl: ^0.20.2
  image_cropper: ^9.0.0
  path_provider: ^2.1.5
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0
  package_info_plus: ^8.3.0
  sensors_plus: ^6.1.1
  syncfusion_flutter_charts: ^28.2.12
  flutter_blue_plus: ^1.35.3
  image_gallery_saver: ^2.0.3
  open_settings_plus: ^0.4.0
  permission_handler: ^11.4.0
  network_info_plus: ^6.1.3
  connectivity_plus: ^6.1.3
  exif: ^3.3.0
  path: ^1.9.0
  torch_light: ^1.1.0
  native_device_orientation: ^2.0.3
  http: ^1.3.0
  vector_math: ^2.1.4
  firebase_analytics: ^11.4.5
  firebase_core: ^3.13.0
  firebase_crashlytics: ^4.3.5
  firebase_remote_config: ^5.4.3
  open_store: ^0.5.0
  flutter_gemini: ^3.0.0
  image_picker: ^1.0.7
  audioplayers: ^6.1.0
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.14.3

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#FFFFFF"
  remove_alpha_ios: true

flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/images/
    - assets/fonts/
    - assets/sounds/
    - assets/json/Alcohol_Substance.json
    - assets/json/BloodGroup.json
    - assets/json/CoffeeTeaNumberofTimesInaDay.json
    - assets/json/Diagnosis.json
    - assets/json/Drugs.json
    - assets/json/Ethnicity.json
    - assets/json/Falls.json
    - assets/json/Hour.json
    - assets/json/MaritalStatus.json
    - assets/json/Meridiem.json
    - assets/json/Minute.json
    - assets/json/NonVegetarian.json
    - assets/json/NumberMedications.json
    - assets/json/OnDiabetesMedicationSince.json
    - assets/json/OnHypertensionMedicationSince.json
    - assets/json/OnThyroidMedicationSince.json
    - assets/json/OtherConditions.json
    - assets/json/Prakriti.json
    - assets/json/QuestionnaireData.json
    - assets/json/Severity.json
    - assets/json/Smoke.json
    - assets/json/Smoking_duration.json
    - assets/json/Smoking_quantity.json
    - assets/json/Smoking_Substance.json
    - assets/json/States.json
    - assets/json/TimeSinceLastMeal.json
    - assets/json/Towns.json
    - assets/json/gender.json
    - assets/gCloud/credentials.json

  fonts:
    - family: Arial
      fonts:
        - asset: assets/fonts/arial.ttf
          weight: 400
        - asset: assets/fonts/G_ari_bd.TTF
          weight: 700
        - asset: assets/fonts/ARIBL0.ttf
          weight: 900
