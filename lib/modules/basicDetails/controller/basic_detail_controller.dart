import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/models/clinic_details_model.dart';
import 'package:gt_plus/models/basic_details_model.dart';
import 'package:gt_plus/models/provider_list_model.dart';
import 'package:gt_plus/modules/meta/view/meta_view.dart';
import 'package:gt_plus/modules/meta/controller/meta_controller.dart';
import 'package:gt_plus/modules/basicDetails/view/basic_detail_view.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:intl/intl.dart';

import '../../../enums/dropdown_type_enum.dart';
import '../../../models/dropdown_type_model.dart';

class BasicDetailController extends GetxController {
  // Subject Information Controllers
  final patientIdController = TextEditingController();
  final dobController = TextEditingController();
  final genderController = TextEditingController();
  final heightFeetController = TextEditingController();
  final heightInchesController = TextEditingController();
  final weightController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final maritalStatusController = TextEditingController();
  final ethnicityController = TextEditingController();
  final educationController = TextEditingController();
  final bloodGroupController = TextEditingController();

  // Other Details Controllers
  final diagnosisController = TextEditingController();
  final otherConditionsController = TextEditingController();
  final hypertensionMedicationController = TextEditingController();
  final diabetesMedicationController = TextEditingController();
  final thyroidMedicationController = TextEditingController();
  final fallsLastYearController = TextEditingController();
  final smokingController = TextEditingController();
  final smokingSubstanceController = TextEditingController();
  final alcoholConsumptionController = TextEditingController();
  final alcoholSubstanceController = TextEditingController();
  final recreationalDrugUseController = TextEditingController();
  final providerNameController = TextEditingController();
  final diagnosisOtherController = TextEditingController();
  final conditionOtherController = TextEditingController();
  // Hearing ability slider value
  final RxDouble hearingAbilityValue = 1.0.obs;

  // Observable dropdown option lists (using the DropdownItem model)
  final genderOptions = <DropdownItem>[].obs;
  final maritalStatusOptions = <DropdownItem>[].obs;
  final ethnicityOptions = <DropdownItem>[].obs;
  final educationOptions = <DropdownItem>[].obs;
  final bloodGroupOptions = <DropdownItem>[].obs;

  // Observable education question text from remote config
  final educationQuestionText = 'Have you completed high school or 12 years of education?'.obs;
  final diagnosisOptions = <DropdownItem>[].obs;
  final otherConditionsOptions = <DropdownItem>[].obs;
  final medicationDurationOptions = <DropdownItem>[].obs;
  final diabetesDurationOption = <DropdownItem>[].obs;
  final thyroidDurationOptions = <DropdownItem>[].obs;
  final yesNoOptions = <DropdownItem>[].obs;
  final smokingOptions = <DropdownItem>[].obs;
  final smokingSubstanceOptions = <DropdownItem>[].obs;
  final alcoholOptions = <DropdownItem>[].obs;
  final alcoholSubstanceOptions = <DropdownItem>[].obs;
  final drugsOptions = <DropdownItem>[].obs;
  final fallsOptions = <DropdownItem>[].obs;
  final statesOptions = <DropdownItem>[].obs;
  final townsOptions = <DropdownItem>[].obs;
  final providerNameOptions = <DropdownItem>[].obs;

  // Observable variables
  RxBool isSmoking = false.obs;
  RxBool isAlcoholConsuming = false.obs;
  final isLoading = true.obs;
  RxBool isDiagnosisOther = false.obs;
  RxBool isConditionOther = false.obs;
  RxBool isDobFromLogin = false.obs;
  RxBool isFirstNameFromLogin = false.obs;
  RxBool isLastNameFromLogin = false.obs;

  // Error messages for validation
  final patientIdError = ''.obs;
  final dobError = ''.obs;
  final genderError = ''.obs;
  final heightFeetError = ''.obs;
  final heightInchesError = ''.obs;
  final weightError = ''.obs;
  final firstNameError = ''.obs;
  final lastNameError = ''.obs;
  final maritalStatusError = ''.obs;
  final ethnicityError = ''.obs;
  final educationError = ''.obs;
  final bloodGroupError = ''.obs;
  final diagnosisError = ''.obs;
  final otherConditionsError = ''.obs;
  final hypertensionMedicationError = ''.obs;
  final diabetesMedicationError = ''.obs;
  final thyroidMedicationError = ''.obs;
  final fallsLastYearError = ''.obs;
  final smokingError = ''.obs;
  final smokingSubstanceError = ''.obs;
  final alcoholConsumptionError = ''.obs;
  final alcoholSubstanceError = ''.obs;
  final recreationalDrugUseError = ''.obs;
  final providerNameError = ''.obs;
  final hearingAbilityError = ''.obs;

  // Read-only mode management
  final RxBool isReadOnly = false.obs;
  final RxBool isFormCompleted = false.obs;
  final RxBool isInitializing = true.obs;

  String? programName;
  final PrefsService _prefsService = PrefsService();
  final ApiService _apiService = ApiService();
  final FirebaseRemoteConfigService _remoteConfigService = FirebaseRemoteConfigService();

  @override
  void onInit() {
    super.onInit();

    // Load all dropdown data from JSON files
    loadAllDropdownData();
    // Listen for smoking status changes
    ever(isSmoking, (_) {
      if (!isSmoking.value) {
        smokingSubstanceController.clear();
      }
    });

    // Initialize form with completion check first
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    try {
      // Check completion status immediately
      await _checkCompletionStatus();

      // Then fetch saved data
      await fetchSavedDetailsData();
    } finally {
      // Mark initialization as complete
      isInitializing.value = false;
    }
  }

  // Method to enable read-only mode
  void enableReadOnlyMode() {
    debugPrint("Enabling read-only mode for basic details form");
    isReadOnly.value = true;
    isFormCompleted.value = true;
  }

  // Method to enable edit mode
  void enableEditMode() {
    isReadOnly.value = false;
  }

  // Method to reset form
  void resetForm() {
    // Clear all controllers
    patientIdController.clear();
    firstNameController.clear();
    lastNameController.clear();
    dobController.clear();
    genderController.clear();
    heightFeetController.clear();
    heightInchesController.clear();
    weightController.clear();
    maritalStatusController.clear();
    ethnicityController.clear();
    bloodGroupController.clear();
    providerNameController.clear();
    otherConditionsController.clear();
    conditionOtherController.clear();
    diabetesMedicationController.clear();
    diagnosisController.clear();
    diagnosisOtherController.clear();
    hypertensionMedicationController.clear();
    thyroidMedicationController.clear();
    fallsLastYearController.clear();
    alcoholConsumptionController.clear();
    recreationalDrugUseController.clear();
    smokingController.clear();
    smokingSubstanceController.clear();
    // Reset hearing ability slider
    hearingAbilityValue.value = 1.0;

    // Reset observables
    isSmoking.value = false;
    isConditionOther.value = false;
    isDiagnosisOther.value = false;
    isDobFromLogin.value = false;
    isFirstNameFromLogin.value = false;
    isLastNameFromLogin.value = false;
    isReadOnly.value = false;
    isFormCompleted.value = false;

    // Clear all error messages
    patientIdError.value = '';
    firstNameError.value = '';
    lastNameError.value = '';
    dobError.value = '';
    genderError.value = '';
    heightFeetError.value = '';
    heightInchesError.value = '';
    weightError.value = '';
    maritalStatusError.value = '';
    ethnicityError.value = '';
    bloodGroupError.value = '';
    otherConditionsError.value = '';
    diabetesMedicationError.value = '';
    diagnosisError.value = '';
    hypertensionMedicationError.value = '';
    thyroidMedicationError.value = '';
    fallsLastYearError.value = '';
    alcoholConsumptionError.value = '';
    alcoholSubstanceError.value = '';
    recreationalDrugUseError.value = '';
    smokingError.value = '';
    smokingSubstanceError.value = '';
    providerNameError.value = '';
    hearingAbilityError.value = '';
  }

  Future<void> getProviderName() async {
    try {
      ClinicDetailsModel? clinicDetailsModel =
          await _prefsService.getClinicDetails();
      programName = clinicDetailsModel?.programName;
      ProviderListModel? providerListModel = await _apiService.getProviders(
        clinicName: clinicDetailsModel?.clinicName ?? "",
        programName: clinicDetailsModel?.programName ?? "",
      );
      
      // Clear existing provider options to prevent duplicates
      providerNameOptions.clear();
      
      if (providerListModel?.providers != null) {
        // Create a Set to ensure we only add unique providers
        final uniqueProviders = <String>{};
        
        // Add each provider with a unique ID
        int id = 0;
        for (String name in providerListModel!.providers!) {
          if (!uniqueProviders.contains(name)) {
            uniqueProviders.add(name);
            providerNameOptions.add(DropdownItem(id: id.toString(), name: name));
            id++;
          }
        }
      }
    } catch (e) {
      debugPrint("Error : $e");
      reusableSnackBar(message: "Something went wrong, we are working on it");
    }
  }

  // Load all dropdown data
  Future<void> loadAllDropdownData() async {
    isLoading.value = true;

    try {
      yesNoOptions.value = [
        DropdownItem(id: "0", name: "No"),
        DropdownItem(id: "1", name: "Yes"),
      ];
      await getProviderName();
      await Future.wait([
        loadDropdownItems(
            DropdownTypesEnum.maritalStatus, maritalStatusOptions),
        loadDropdownItems(DropdownTypesEnum.ethnicity, ethnicityOptions),
        loadEducationOptionsFromRemoteConfig(),
        loadDropdownItems(DropdownTypesEnum.bloodGroup, bloodGroupOptions),
        loadDropdownItems(DropdownTypesEnum.diagnosis, diagnosisOptions),
        loadDropdownItems(
            DropdownTypesEnum.otherConditions, otherConditionsOptions),
        loadDropdownItems(DropdownTypesEnum.onHypertensionMedicationSince,
            medicationDurationOptions),
        loadDropdownItems(
            DropdownTypesEnum.onThyroidMedicationSince, thyroidDurationOptions),
        loadDropdownItems(DropdownTypesEnum.onDiabetesMedicationSince,
            diabetesDurationOption),
        loadDropdownItems(DropdownTypesEnum.smoke, smokingOptions),
        loadDropdownItems(
            DropdownTypesEnum.smokingSubstance, smokingSubstanceOptions),
        loadDropdownItems(DropdownTypesEnum.alcohol, alcoholSubstanceOptions),
        loadDropdownItems(DropdownTypesEnum.drugs, drugsOptions),
        loadDropdownItems(DropdownTypesEnum.falls, fallsOptions),
        loadDropdownItems(DropdownTypesEnum.states, statesOptions),
        loadDropdownItems(DropdownTypesEnum.towns, townsOptions),
        loadDropdownItems(DropdownTypesEnum.gender, genderOptions),
      ]);
    } catch (e) {
      debugPrint('Error loading dropdown data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadDropdownItems(
      DropdownTypesEnum type, RxList<DropdownItem> targetList) async {
    try {
      final items = await loadJsonAsDropdownItems(type);
      targetList.value = items;
    } catch (e) {
      debugPrint('Error loading ${type.name} dropdown items: $e');
    }
  }

  Future<List<DropdownItem>> loadJsonAsDropdownItems(
      DropdownTypesEnum type) async {
    try {
      String jsonString = await rootBundle.loadString(type.path);
      List<dynamic> jsonResponse = json.decode(jsonString);
      return jsonResponse.map((item) => DropdownItem.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Error loading JSON data for ${type.name}: $e');
      return [];
    }
  }

  Future<void> loadEducationOptionsFromRemoteConfig() async {
    try {
      final qualificationData = _remoteConfigService.getQualificationQuestion();

      if (qualificationData != null && qualificationData['options'] != null) {
        // Set the question text from remote config
        if (qualificationData['question'] != null) {
          educationQuestionText.value = qualificationData['question'].toString();
        }

        final options = qualificationData['options'] as List<dynamic>;
        final dropdownItems = options.map((option) {
          return DropdownItem(
            id: option['value'].toString(),
            name: option['label'].toString(),
          );
        }).toList();

        educationOptions.value = dropdownItems;
        debugPrint('Loaded education options from remote config: ${dropdownItems.length} items');
        debugPrint('Education question text: ${educationQuestionText.value}');
      } else {
        // No fallback - education data must come from remote config
        debugPrint('ERROR: No qualification data found in remote config. Education options will be empty.');
        educationOptions.value = [];
      }
    } catch (e) {
      debugPrint('ERROR: Failed to load education options from remote config: $e');
      debugPrint('Education options will be empty as no fallback is available.');
      educationOptions.value = [];
    }
  }

  String getSelectedText(
      TextEditingController controller, List<DropdownItem> options) {
    final selectedItem = options.firstWhere(
      (item) => item.id == controller.text,
      orElse: () => DropdownItem(id: "", name: ""),
    );
    return selectedItem.name;
  }

  String? getIdFromName(String name, List<DropdownItem> options) {
    final item = options.firstWhere(
      (item) => item.name == name,
      orElse: () => DropdownItem(id: "", name: ""),
    );
    return item.id.isEmpty ? null : item.id;
  }

  @override
  void onClose() {
    patientIdController.dispose();
    dobController.dispose();
    genderController.dispose();
    heightFeetController.dispose();
    heightInchesController.dispose();
    weightController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    maritalStatusController.dispose();
    ethnicityController.dispose();
    bloodGroupController.dispose();
    diagnosisController.dispose();
    otherConditionsController.dispose();
    hypertensionMedicationController.dispose();
    diabetesMedicationController.dispose();
    thyroidMedicationController.dispose();
    fallsLastYearController.dispose();
    smokingController.dispose();
    smokingSubstanceController.dispose();
    alcoholConsumptionController.dispose();
    alcoholSubstanceController.dispose();
    recreationalDrugUseController.dispose();
    providerNameController.dispose();
    diagnosisOtherController.dispose();
    conditionOtherController.dispose();
    // hearingAbilityValue doesn't need disposal as it's an RxDouble
    super.onClose();
  }

  // Get dropdown display list for UI (only names)
  List<String> getDropdownNamesList(List<DropdownItem> items) {
    return items.map((item) => item.name).toList();
  }

  // Set controller value with ID when user selects an item by name
  void setControllerValueFromSelection(TextEditingController controller,
      String selectedName, List<DropdownItem> options) {
    final selectedId = getIdFromName(selectedName, options);
    if (selectedId != null) {
      controller.text = selectedId;
    }
  }

  bool validateDOB(String dobText) {
    if (dobText.isEmpty) {
      dobError.value = 'Enter a valid DOB';
      return false;
    }

    try {
      List<String> parts = dobText.split('/');
      if (parts.length != 3 || parts[2].length != 4) {
        dobError.value = 'Enter a valid DOB';
        return false;
      }

      DateTime dob = DateFormat('MM/dd/yyyy').parseStrict(dobText);
      DateTime today = DateTime.now();
      DateTime minDate = DateTime(1900, 1, 1);

      if (dob.isAfter(today) || dob.isBefore(minDate)) {
        dobError.value = 'Enter a valid DOB';
        return false;
      }

      dobError.value = '';
      return true;
    } catch (e) {
      dobError.value = 'Enter a valid DOB';
      return false;
    }
  }

  // Validate all fields
  bool validateForm() {
    bool isValid = true;

    // Subject Information validation
    // Patient ID validation (only if not empty, since it's optional)
    if (patientIdController.text.isNotEmpty) {
      // Check if Patient ID contains only digits
      if (!RegExp(r'^\d+$').hasMatch(patientIdController.text)) {
        patientIdError.value = 'Patient ID must contain digits only';
        isValid = false;
      } else {
        patientIdError.value = '';
      }
    } else {
      patientIdError.value = '';
    }

    isValid = validateDOB(dobController.text);
    if (genderController.text.isEmpty) {
      genderError.value = 'Gender is required';
      isValid = false;
    } else {
      genderError.value = '';
    }

    if (heightFeetController.text.isEmpty) {
      heightFeetError.value = 'Feet is required';
      isValid = false;
    } else {
      // Parse the feet value and validate it's in a reasonable range
      try {
        final feetValue = int.parse(heightFeetController.text);
        if (feetValue < 0 || feetValue > 9) {
          heightFeetError.value = 'Feet must be between 0 and 9';
          isValid = false;
        } else {
          heightFeetError.value = '';
        }
      } catch (e) {
        heightFeetError.value = 'Please enter a valid number';
        isValid = false;
      }
    }

    if (heightInchesController.text.isEmpty) {
      heightInchesError.value = 'Inches is required';
      isValid = false;
    } else {
      try {
        final inchesValue = int.parse(heightInchesController.text);
        if (inchesValue < 0 || inchesValue > 11) {
          heightInchesError.value = 'Inches must be between 0 and 11';
          isValid = false;
        } else {
          heightInchesError.value = '';
        }
      } catch (e) {
        heightInchesError.value = 'Please enter a valid number';
        isValid = false;
      }
    }

    if (weightController.text.isEmpty) {
      weightError.value = 'Weight is required';
      isValid = false;
    } else if (!RegExp(r'^\d+$').hasMatch(weightController.text)) {
      weightError.value = 'Weight must contain digits only';
      isValid = false;
    } else {
      weightError.value = '';
    }

    RegExp nameRegExp = RegExp(r'^[a-zA-Z]+$');
    if (firstNameController.text.isEmpty) {
      firstNameError.value = 'First Name is required';
      isValid = false;
    } else if (!nameRegExp.hasMatch(firstNameController.text)) {
      firstNameError.value = 'First Name should contain only letters';
      isValid = false;
    } else {
      firstNameError.value = '';
    }

    if (lastNameController.text.isEmpty) {
      lastNameError.value = 'Last Name is required';
      isValid = false;
    } else if (!nameRegExp.hasMatch(lastNameController.text)) {
      lastNameError.value = 'Last Name should contain only letters';
      isValid = false;
    } else {
      lastNameError.value = '';
    }

    if (maritalStatusController.text.isEmpty) {
      maritalStatusError.value = 'Marital Status is required';
      isValid = false;
    } else {
      maritalStatusError.value = '';
    }

    if (ethnicityController.text.isEmpty) {
      ethnicityError.value = 'Ethnicity/Race is required';
      isValid = false;
    } else {
      ethnicityError.value = '';
    }

    if (educationController.text.isEmpty) {
      educationError.value = 'Education is required';
      isValid = false;
    } else {
      educationError.value = '';
    }

    // Other Details validation
    // Diagnosis is now optional - only validate if "Other" is selected and text is empty
    if (isDiagnosisOther.value && diagnosisOtherController.text.isEmpty) {
      diagnosisError.value = 'Please specify diagnosis';
      isValid = false;
    } else {
      diagnosisError.value = '';
    }

    // Other Details validation
    if (providerNameController.text.isEmpty) {
      providerNameError.value = 'Provider name is required';
      isValid = false;
    } else {
      providerNameError.value = '';
    }

    if (otherConditionsController.text.isEmpty && !isConditionOther.value) {
      otherConditionsError.value = 'Other Conditions is required';
      isValid = false;
    } else if (isConditionOther.value && conditionOtherController.text.isEmpty) {
      otherConditionsError.value = 'Please specify condition';
      isValid = false;
    } else {
      otherConditionsError.value = '';
    }

    if (hypertensionMedicationController.text.isEmpty) {
      hypertensionMedicationError.value =
          'Hypertension Medication info is required';
      isValid = false;
    } else {
      hypertensionMedicationError.value = '';
    }

    if (diabetesMedicationController.text.isEmpty) {
      diabetesMedicationError.value = 'Diabetes Medication info is required';
      isValid = false;
    } else {
      diabetesMedicationError.value = '';
    }

    if (thyroidMedicationController.text.isEmpty) {
      thyroidMedicationError.value = 'Thyroid Medication info is required';
      isValid = false;
    } else {
      thyroidMedicationError.value = '';
    }

    if (fallsLastYearController.text.isEmpty) {
      fallsLastYearError.value = 'Falls information is required';
      isValid = false;
    } else {
      fallsLastYearError.value = '';
    }

    if (smokingController.text.isEmpty) {
      smokingError.value = 'Smoking information is required';
      isValid = false;
    } else {
      smokingError.value = '';

      // Check if smoking substance is required
      if (smokingController.text == getIdFromName('Yes', yesNoOptions) &&
          smokingSubstanceController.text.isEmpty) {
        smokingSubstanceError.value = 'Smoking Substance is required';
        isValid = false;
      } else {
        smokingSubstanceError.value = '';
      }
    }

    if (alcoholConsumptionController.text.isEmpty) {
      alcoholConsumptionError.value =
          'Alcohol Consumption information is required';
      isValid = false;
    } else {
      alcoholConsumptionError.value = '';

      // Check if alcohol substance is required
      if (alcoholConsumptionController.text == getIdFromName('Yes', yesNoOptions) &&
          alcoholSubstanceController.text.isEmpty) {
        alcoholSubstanceError.value = 'Alcohol Substance is required';
        isValid = false;
      } else {
        alcoholSubstanceError.value = '';
      }
    }

    if (bloodGroupController.text.isEmpty) {
      bloodGroupError.value = 'Blood  Group is required';
      isValid = false;
    } else {
      bloodGroupError.value = '';
    }

    if (recreationalDrugUseController.text.isEmpty) {
      recreationalDrugUseError.value =
          'Recreational Drug Use information is required';
      isValid = false;
    } else {
      recreationalDrugUseError.value = '';
    }

    // Validate hearing ability (slider value is always valid between 1-10)
    if (hearingAbilityValue.value < 1 || hearingAbilityValue.value > 10) {
      hearingAbilityError.value = 'Hearing ability value must be between 1 and 10';
      isValid = false;
    } else {
      hearingAbilityError.value = '';
    }

    return isValid;
  }

  Future<Map<String, dynamic>> getBasicDetailData() async {
    ClinicDetailsModel? clinicModel = await _prefsService.getClinicDetails();
    
    // Get provider name from ID
    String providerName = "";
    if (providerNameController.text.isNotEmpty) {
      final provider = providerNameOptions.firstWhere(
        (item) => item.id == providerNameController.text,
        orElse: () => DropdownItem(id: "", name: ""),
      );
      if (provider.name.isNotEmpty) {
        providerName = provider.name;
      }
    }
    
    // Get list of selected diagnosis names
    final diagnosisList = getSelectedDiagnosisNames();
    
    // Get list of other conditions
    final otherConditionsList = getSelectedOtherConditionNames();
    final otherConditionsValue = otherConditionsList.isEmpty 
        ? "" 
        : otherConditionsList.join(', ');
    
    return {
      // Subject Information
      "patientId": patientIdController.text,
      "dateOfBirth": dobController.text,
      "gender": genderController.text,
      "feet": heightFeetController.text,
      "inches": heightInchesController.text,
      "lbsWeight": weightController.text,
      "firstName": firstNameController.text,
      "lastName": lastNameController.text,
      "maritalStatus": maritalStatusController.text,
      "ethnicity": ethnicityController.text,
      "education": educationController.text,
      "bloodGroup": bloodGroupController.text,
      "clinic": clinicModel?.clinicName,
      "providerName": providerName,
      "program": programName,
      "hearingAbility": hearingAbilityValue.value.round().toString(),

      // Other Details
      "hypertensionMedication": hypertensionMedicationController.text,
      "diabetesMedication": diabetesMedicationController.text,
      "thyroidMedication": thyroidMedicationController.text,
      "fallsLastYear": fallsLastYearController.text,
      "smoking": smokingController.text,
      "smokingSubstance": smokingSubstanceController.text,
      "alcoholConsumption": alcoholConsumptionController.text,
      "alcoholSubstance": alcoholSubstanceController.text,
      "recreationalDrugUse": recreationalDrugUseController.text,
      "diagnosis": diagnosisList,
      "otherConditions": otherConditionsValue,
    };
  }

  Future<void> onSubmitClick() async {
    isLoading.value = true;
    try {
      if (validateForm()) {
        Map<String, dynamic> basicDetailData = await getBasicDetailData();
        bool isDataPushed = await _apiService.postDetails(
          type: BasicDetailTypes.BasicDetails,
          detailsData: basicDetailData,
        );
        if (isDataPushed) {
          // Store updated patient information in preferences for future prefilling
          String fullName = "${firstNameController.text.trim()} ${lastNameController.text.trim()}";
          await _prefsService.setPatientName(fullName);
          await _prefsService.setFirstNameFromLogin(firstNameController.text.trim());
          await _prefsService.setLastNameFromLogin(lastNameController.text.trim());
          await _prefsService.setDobFromLogin(dobController.text);

          Get.offAllNamed(MetaView.routeName);
        } else {
          reusableSnackBar(
              message: "Something went wrong, we are working on it");
        }
      } else {
        reusableSnackBar(message: "Please check your input");
      }
    } catch (e) {
      debugPrint("Error : $e");
      reusableSnackBar(message: "Something went wrong, we are working on it");
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch saved details data from API
  Future<void> fetchSavedDetailsData() async {
    try {
      // Check if DOB and names were provided during login
      final String loginMethod = await _prefsService.getLoginMethod();
      if (loginMethod == "dob") {
        final String dobFromLogin = await _prefsService.getDobFromLogin();
        if (dobFromLogin.isNotEmpty) {
          dobController.text = dobFromLogin;
          isDobFromLogin.value = true;
        }

        // Also prefill first name and last name from login
        final String firstNameFromLogin = await _prefsService.getFirstNameFromLogin();
        final String lastNameFromLogin = await _prefsService.getLastNameFromLogin();
        if (firstNameFromLogin.isNotEmpty) {
          firstNameController.text = firstNameFromLogin;
          isFirstNameFromLogin.value = true;
        }
        if (lastNameFromLogin.isNotEmpty) {
          lastNameController.text = lastNameFromLogin;
          isLastNameFromLogin.value = true;
        }
      }
      
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.BasicDetails,
      );

      if (response != null) {
        final basicDetails = BasicDetailsModel.fromJson(response);

        // Only set first name if not already set from login
        if (!isFirstNameFromLogin.value && basicDetails.firstName.isNotEmpty) {
          firstNameController.text = basicDetails.firstName;
        }

        // Only set last name if not already set from login
        if (!isLastNameFromLogin.value && basicDetails.lastName.isNotEmpty) {
          lastNameController.text = basicDetails.lastName;
        }

        // Only set DOB if not already set from login
        if (!isDobFromLogin.value) {
          dobController.text = basicDetails.dateOfBirth;
        }

        // Set gender if available
        if (basicDetails.gender.isNotEmpty) {
          genderController.text = basicDetails.gender;
        }

        // Set height and weight if available
        heightFeetController.text = basicDetails.feet;
        heightInchesController.text = basicDetails.inches;
        weightController.text = basicDetails.lbsWeight;

        // Set marital status if available
        if (basicDetails.maritalStatus.isNotEmpty) {
          maritalStatusController.text = basicDetails.maritalStatus;
        }

        // Set ethnicity if available
        if (basicDetails.ethnicity.isNotEmpty) {
          ethnicityController.text = basicDetails.ethnicity;
        }

        // Set education if available
        if (basicDetails.education.isNotEmpty) {
          educationController.text = basicDetails.education;
        }

        // Set blood group if available
        if (basicDetails.bloodGroup.isNotEmpty) {
          bloodGroupController.text = basicDetails.bloodGroup;
        }

        // Set patient ID if available
        patientIdController.text = basicDetails.patientId;

        // Set hearing ability if available
        if (basicDetails.hearingAbility.isNotEmpty) {
          try {
            hearingAbilityValue.value = double.parse(basicDetails.hearingAbility);
          } catch (e) {
            // If parsing fails, set to default value
            hearingAbilityValue.value = 1.0;
          }
        }

        // Set diagnosis if available
        if (basicDetails.diagnosis.isNotEmpty) {
          await _loadDropdownDataIfNeeded();
          
          // Process each diagnosis in the list
          List<String> diagnosisIds = [];
          bool hasOtherDiagnosis = false;
          String otherDiagnosisText = "";
          
          for (String diagnosisName in basicDetails.diagnosis) {
            // Check if this diagnosis is in our dropdown options
            final diagnosisItem = diagnosisOptions.firstWhere(
              (item) => item.name == diagnosisName,
              orElse: () {
                // Not found in list, mark as "Other"
                hasOtherDiagnosis = true;
                otherDiagnosisText = diagnosisName;
                return DropdownItem(id: "", name: "");
              },
            );
            
            if (diagnosisItem.id.isNotEmpty) {
              diagnosisIds.add(diagnosisItem.id);
            }
          }
          
          if (diagnosisIds.isNotEmpty) {
            diagnosisController.text = diagnosisIds.join(', ');
          }
          
          if (hasOtherDiagnosis) {
            // Add "Other" to the selection if not already there
            final otherId = diagnosisOptions.firstWhere(
              (item) => item.name == "Other",
              orElse: () => DropdownItem(id: "", name: ""),
            ).id;
            
            if (diagnosisController.text.isNotEmpty) {
              diagnosisController.text += ', $otherId';
            } else {
              diagnosisController.text = otherId;
            }
            
            isDiagnosisOther.value = true;
            diagnosisOtherController.text = otherDiagnosisText;
          }
        }

        // Set other conditions if available
        if (basicDetails.otherConditions.isNotEmpty) {
          await _loadDropdownDataIfNeeded();
          
          // Split multiple conditions by comma
          List<String> conditionNames = basicDetails.otherConditions.split(',')
              .map((e) => e.trim())
              .toList();
          
          List<String> conditionIds = [];
          bool hasOtherCondition = false;
          String otherConditionText = "";
          
          for (String conditionName in conditionNames) {
            // Check if this condition is in our dropdown options
            final conditionItem = otherConditionsOptions.firstWhere(
              (item) => item.name == conditionName,
              orElse: () {
                // Not found in list, mark as "Other"
                hasOtherCondition = true;
                otherConditionText = conditionName;
                return DropdownItem(id: "", name: "");
              },
            );
            
            if (conditionItem.id.isNotEmpty) {
              conditionIds.add(conditionItem.id);
            }
          }
          
          if (conditionIds.isNotEmpty) {
            otherConditionsController.text = conditionIds.join(', ');
          }
          
          if (hasOtherCondition) {
            // Add "Other" to the selection if not already there
            final otherId = otherConditionsOptions.firstWhere(
              (item) => item.name == "Other",
              orElse: () => DropdownItem(id: "", name: ""),
            ).id;
            
            if (otherId.isNotEmpty) {
              if (otherConditionsController.text.isNotEmpty) {
                otherConditionsController.text += ', $otherId';
              } else {
                otherConditionsController.text = otherId;
              }
              
              isConditionOther.value = true;
              conditionOtherController.text = otherConditionText;
            }
          }
        }

        // Set medication information
        hypertensionMedicationController.text =
            basicDetails.hypertensionMedication;
        diabetesMedicationController.text = basicDetails.diabetesMedication;
        thyroidMedicationController.text = basicDetails.thyroidMedication;

        // Set falls information
        fallsLastYearController.text = basicDetails.fallsLastYear;

        // Set smoking information
        smokingController.text = basicDetails.smoking;
        isSmoking.value =
            basicDetails.smoking == "1"; // Assuming "1" means "Yes"
        if (isSmoking.value) {
          smokingSubstanceController.text = basicDetails.smokingSubstance;
        }

        // Set alcohol and drug use information
        alcoholConsumptionController.text = basicDetails.alcoholConsumption;
        isAlcoholConsuming.value =
            basicDetails.alcoholConsumption == "1"; // Assuming "1" means "Yes"
        if (isAlcoholConsuming.value) {
          alcoholSubstanceController.text = basicDetails.alcoholSubstance;
        }
        recreationalDrugUseController.text = basicDetails.recreationalDrugUse;

        // Set provider name if available
        if (basicDetails.providerName.isNotEmpty) {
          await _loadDropdownDataIfNeeded();
          
          // Find the matching provider name by name (not ID)
          final providerItem = providerNameOptions.firstWhere(
            (item) => item.name == basicDetails.providerName,
            orElse: () => DropdownItem(id: "", name: ""),
          );
          
          if (providerItem.id.isNotEmpty) {
            providerNameController.text = providerItem.id;
          } else {
            // If no matching provider is found, request providers again and retry
            await getProviderName();
            
            // Try finding the provider again after refreshing the list
            final retryProviderItem = providerNameOptions.firstWhere(
              (item) => item.name == basicDetails.providerName,
              orElse: () => DropdownItem(id: "", name: ""),
            );
            
            if (retryProviderItem.id.isNotEmpty) {
              providerNameController.text = retryProviderItem.id;
            } else {
              // If still not found, log the issue but don't set the controller
              // to avoid dropdown validation errors
              debugPrint("Provider name not found in options: ${basicDetails.providerName}");
            }
          }
        }
      }
    } catch (e) {
      debugPrint("Error fetching saved basic details: $e");
    }
  }

  // Check completion status from MetaController
  Future<void> _checkCompletionStatus() async {
    try {
      final metaController = Get.find<MetaController>();
      const routeName = BasicDetailView.routeName;
      final isCompleted = metaController.moduleCompletionStatus[routeName] ?? false;
      final hasCompletedGTPlusOnce = metaController.hasPatientCompletedGTPlusOnce();

      debugPrint("Basic Details route name: $routeName");
      debugPrint("Basic Details completion status: $isCompleted");
      debugPrint("Has patient completed GT+ once: $hasCompletedGTPlusOnce");
      debugPrint("All completion statuses: ${metaController.moduleCompletionStatus}");

      if (isCompleted || hasCompletedGTPlusOnce) {
        debugPrint("Setting basic details form to read-only mode");
        enableReadOnlyMode();
      }
    } catch (e) {
      // MetaController might not be initialized yet, that's okay
      debugPrint("MetaController not found or not initialized: $e");
    }
  }
  
  // Helper method to ensure dropdown data is loaded
  Future<void> _loadDropdownDataIfNeeded() async {
    if (diagnosisOptions.isEmpty || otherConditionsOptions.isEmpty || providerNameOptions.isEmpty) {
      await loadAllDropdownData();
    }
  }

  // Diagnosis multiselect update method
  List<String> getSelectedDiagnosisNames() {
    if (diagnosisController.text.isEmpty) return [];
    
    final selectedIds = diagnosisController.text.split(', ');
    final names = <String>[];
    
    for (final id in selectedIds) {
      final item = diagnosisOptions.firstWhere(
        (item) => item.id == id,
        orElse: () => DropdownItem(id: "", name: ""),
      );
      if (item.name.isNotEmpty && item.name != "Other") {
        names.add(item.name);
      }
    }
    
    // Add "Other" diagnosis if applicable
    if (isDiagnosisOther.value && diagnosisOtherController.text.isNotEmpty) {
      names.add(diagnosisOtherController.text);
    }
    
    return names;
  }

  // Other conditions multiselect update method
  List<String> getSelectedOtherConditionNames() {
    if (otherConditionsController.text.isEmpty) return [];
    
    final selectedIds = otherConditionsController.text.split(', ');
    final names = <String>[];
    
    for (final id in selectedIds) {
      final item = otherConditionsOptions.firstWhere(
        (item) => item.id == id,
        orElse: () => DropdownItem(id: "", name: ""),
      );
      if (item.name.isNotEmpty && item.name != "Other") {
        names.add(item.name);
      }
    }
    
    // Add "Other" condition if applicable
    if (isConditionOther.value && conditionOtherController.text.isNotEmpty) {
      names.add(conditionOtherController.text);
    }
    
    return names;
  }
}
